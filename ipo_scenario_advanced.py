# ipo_scenario_advanced.py
# Phase 2: Advanced IPO Scenario features with custom distributions and fee allocation

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

def validate_custom_percentages(percentages):
    """
    Validate custom percentage allocations.
    
    Args:
        percentages: List of percentage values
    
    Returns:
        Tuple (is_valid, error_message)
    """
    if not percentages:
        return False, "No percentages provided"
    
    if len(percentages) == 0:
        return False, "At least one percentage is required"
    
    # Check all are numbers
    try:
        percentages = [float(p) for p in percentages]
    except (ValueError, TypeError):
        return False, "All percentages must be numeric values"
    
    # Check all are positive
    if any(p < 0 for p in percentages):
        return False, "All percentages must be positive"
    
    # Check sum equals 100
    total = sum(percentages)
    if abs(total - 100) > 0.01:  # Allow small floating point errors
        return False, f"Percentages must sum to 100% (current sum: {total:.2f}%)"
    
    return True, None

def calculate_ipo_distributions_advanced(
    company_name, exit_value, exit_date, 
    first_sale_date=None,  # NEW: When first distribution occurs
    distribution_years=3, 
    distribution_pattern='equal',
    custom_percentages=None,
    distribution_frequency='semi-annual',
    custom_months_between=None,
    original_investment=None
):
    """
    Calculate IPO distribution schedule with advanced options.
    
    Args:
        company_name: Name of the company
        exit_value: Total exit value
        exit_date: Exit date (when final distribution occurs)
        first_sale_date: Optional first sale date (overrides automatic calculation)
        distribution_years: Number of years for distributions (default 3)
        distribution_pattern: 'equal' or 'custom'
        custom_percentages: List of percentages for custom pattern
        distribution_frequency: 'quarterly', 'semi-annual', 'annual', or 'custom'
        custom_months_between: For custom frequency, months between distributions
        original_investment: Original investment amount (for fee calculations)
    
    Returns:
        Dictionary with distribution schedule
    """
    import pandas as pd
    from dateutil.relativedelta import relativedelta
    # Determine months between distributions
    if distribution_frequency == 'quarterly':
        months_between = 3
        distributions_per_year = 4
    elif distribution_frequency == 'annual':
        months_between = 12
        distributions_per_year = 1
    elif distribution_frequency == 'custom' and custom_months_between:
        months_between = custom_months_between
        distributions_per_year = 12 / months_between
    else:  # semi-annual (default)
        months_between = 6
        distributions_per_year = 2
    
    # Calculate first_sale_date and num_distributions
    if first_sale_date is None:
        # Calculate first_sale_date by working backwards from exit_date
        if distribution_frequency == 'custom' and custom_months_between:
            total_months = distribution_years * 12
            num_distributions = max(1, total_months // months_between)
        else:
            num_distributions = distribution_years * distributions_per_year
        
        # Calculate first_sale_date by working backwards from exit_date
        total_distribution_period = months_between * (num_distributions - 1)
        first_sale_date = exit_date - relativedelta(months=total_distribution_period)
    else:
        # Calculate number of distributions from the date range
        first_sale_date = pd.to_datetime(first_sale_date)
        exit_date = pd.to_datetime(exit_date)
        
        # Calculate total months between first sale and final exit
        total_months = (exit_date.year - first_sale_date.year) * 12 + (exit_date.month - first_sale_date.month)
        
        # Calculate number of distributions that fit in this period
        num_distributions = max(1, (total_months // months_between) + 1)  # +1 to include final distribution
    
    # Calculate distribution dates from first_sale_date to exit_date
    distribution_dates = []
    for i in range(num_distributions):
        if i == num_distributions - 1:
            # Last distribution is exactly on exit_date
            dist_date = exit_date
        else:
            # Calculate intermediate dates
            dist_date = first_sale_date + relativedelta(months=months_between * i)
        distribution_dates.append(dist_date)
    
    # Calculate distribution percentages based on pattern
    if distribution_pattern == 'custom' and custom_percentages:
        # Validate and use custom percentages
        is_valid, error_msg = validate_custom_percentages(custom_percentages)
        if not is_valid:
            # Fall back to equal distribution
            distribution_percentages = [100 / num_distributions] * num_distributions
        else:
            # Ensure we have the right number of percentages
            if len(custom_percentages) != num_distributions:
                # Interpolate or adjust
                if len(custom_percentages) < num_distributions:
                    # Pad with zeros
                    distribution_percentages = list(custom_percentages) + [0] * (num_distributions - len(custom_percentages))
                else:
                    # Truncate
                    distribution_percentages = custom_percentages[:num_distributions]
            else:
                distribution_percentages = custom_percentages
    elif distribution_pattern == 'equal':
        distribution_percentages = [100 / num_distributions] * num_distributions
    else:
        # Default to equal if pattern not recognized
        distribution_percentages = [100 / num_distributions] * num_distributions
    
    # Calculate actual amounts
    distribution_amounts = [exit_value * (pct / 100) for pct in distribution_percentages]
    
    return {
        'company_name': company_name,
        'exit_value': exit_value,
        'num_distributions': num_distributions,
        'distribution_dates': distribution_dates,
        'distribution_amounts': distribution_amounts,
        'distribution_percentages': distribution_percentages,
        'pattern': distribution_pattern,
        'frequency': distribution_frequency,
        'custom_percentages': custom_percentages if distribution_pattern == 'custom' else None,
        'original_investment': original_investment,
        'distribution_years': distribution_years,
        'first_sale_date': first_sale_date,  # NEW: Store first sale date
        'final_exit_date': exit_date  # NEW: Store final exit date
    }

def display_custom_percentage_editor(num_distributions, key_prefix):
    """
    Display an editor for custom percentage allocation.
    
    Args:
        num_distributions: Number of distributions
        key_prefix: Prefix for session state keys
    
    Returns:
        List of percentages or None if invalid
    """
    st.markdown("##### Custom Distribution Percentages")
    st.info(f"Enter {num_distributions} percentages that sum to 100%")
    
    # Initialize session state if not exists or if number of distributions changed
    if f'{key_prefix}_custom_pcts' not in st.session_state:
        # Default to equal distribution
        default_pcts = [100 / num_distributions] * num_distributions
        st.session_state[f'{key_prefix}_custom_pcts'] = default_pcts
    elif len(st.session_state[f'{key_prefix}_custom_pcts']) != num_distributions:
        # Number of distributions changed - reset to equal distribution
        st.info(f"📅 Number of distributions changed to {num_distributions}. Percentages reset to equal distribution.")
        default_pcts = [100 / num_distributions] * num_distributions
        st.session_state[f'{key_prefix}_custom_pcts'] = default_pcts
    
    # Create input columns
    cols_per_row = min(4, num_distributions)
    rows_needed = (num_distributions + cols_per_row - 1) // cols_per_row
    
    percentages = []
    dist_num = 0
    
    for row in range(rows_needed):
        cols = st.columns(cols_per_row)
        for col_idx in range(cols_per_row):
            if dist_num < num_distributions:
                with cols[col_idx]:
                    # Safeguard against index out of range
                    try:
                        default_value = st.session_state[f'{key_prefix}_custom_pcts'][dist_num]
                    except (IndexError, KeyError):
                        default_value = 100 / num_distributions
                    
                    pct = st.number_input(
                        f"Dist {dist_num + 1} (%)",
                        min_value=0.0,
                        max_value=100.0,
                        value=default_value,
                        step=0.1,
                        key=f"{key_prefix}_pct_{dist_num}"
                    )
                    percentages.append(pct)
                dist_num += 1
    
    # Validate and show summary
    total = sum(percentages)
    col1, col2 = st.columns(2)
    
    with col1:
        if abs(total - 100) < 0.01:
            st.success(f"✓ Total: {total:.1f}%")
        else:
            st.error(f"✗ Total: {total:.1f}% (must equal 100%)")
    
    with col2:
        if st.button("Reset to Equal", key=f"{key_prefix}_reset"):
            equal_pcts = [100 / num_distributions] * num_distributions
            st.session_state[f'{key_prefix}_custom_pcts'] = equal_pcts
            st.rerun()
    
    # Update session state
    st.session_state[f'{key_prefix}_custom_pcts'] = percentages
    
    # Return percentages if valid
    is_valid, _ = validate_custom_percentages(percentages)
    return percentages if is_valid else None

def apply_enhanced_fees_to_ipo_distributions(schedule, fee_entry, is_first_exit=False):
    """
    Apply management fees to IPO distributions using decremental allocation.
    
    Args:
        schedule: IPO distribution schedule
        fee_entry: Dictionary containing fee breakdown (Management Fees, Capital Call Fees, Priority Return)
        is_first_exit: Whether this is the first exit for the company
    
    Returns:
        Modified schedule with fees applied
    """
    from dateutil.relativedelta import relativedelta
    
    distribution_amounts = schedule['distribution_amounts'].copy()
    
    # Separate fee types
    management_fees = fee_entry.get('Management Fees', 0)
    capital_call_fees = fee_entry.get('Capital Call Fees', 0) if is_first_exit else 0  # Only on first exit
    priority_return = fee_entry.get('Priority Return', 0) if is_first_exit else 0   # Only on first exit
    
    # Initialize fee tracking arrays
    management_fee_amounts = [0] * len(distribution_amounts)
    capital_call_fee_amounts = [0] * len(distribution_amounts)
    priority_return_amounts = [0] * len(distribution_amounts)
    
    # Apply Management Fees using decremental allocation
    if management_fees > 0:
        # Management fees continue to accrue quarterly throughout IPO distribution period
        # Fees decrement at each distribution as cost basis decreases when shares are sold
        
        # Get original investment (cost basis)
        if 'original_investment' in schedule and schedule['original_investment']:
            initial_cost_basis = schedule['original_investment']
        else:
            # Estimate from management fees if original investment not available
            estimated_annual_rate = 0.025  # 2.5% annual management fee assumption
            distribution_period_years = schedule.get('distribution_years', 3)
            initial_cost_basis = management_fees / (estimated_annual_rate * distribution_period_years)
        
        # Set up quarterly management fee calculation
        quarterly_rate = 0.025 / 4  # 2.5% annual rate / 4 quarters = 0.625% quarterly
        
        # Get distribution dates from schedule (always needed)
        distribution_dates = schedule['distribution_dates']
        
        # Get IPO exit date (use first_sale_date if available, otherwise calculate from first distribution)
        if 'first_sale_date' in schedule and schedule['first_sale_date']:
            ipo_exit_date = schedule['first_sale_date']
        else:
            # Fallback to existing logic
            if schedule.get('frequency') == 'quarterly':
                ipo_exit_date = distribution_dates[0] - relativedelta(months=3)
            elif schedule.get('frequency') == 'annual':
                ipo_exit_date = distribution_dates[0] - relativedelta(months=12)
            else:  # semi-annual or default
                ipo_exit_date = distribution_dates[0] - relativedelta(months=6)
        
        # Track remaining cost basis throughout distribution period
        remaining_cost_basis = initial_cost_basis
        last_fee_calc_date = ipo_exit_date
        
        # Calculate fees for each distribution event
        for i in range(len(distribution_amounts)):
            distribution_date = distribution_dates[i]
            
            # Get the percentage of shares being distributed
            distribution_percentage = schedule['distribution_percentages'][i] / 100
            
            # Calculate quarters between last fee calculation and this distribution
            months_elapsed = (distribution_date.year - last_fee_calc_date.year) * 12 + \
                           (distribution_date.month - last_fee_calc_date.month)
            quarters_elapsed = months_elapsed / 3.0
            
            # Calculate accumulated management fees for this period
            if remaining_cost_basis > 0 and quarters_elapsed > 0:
                accumulated_fees = remaining_cost_basis * quarterly_rate * quarters_elapsed
                management_fee_amounts[i] = accumulated_fees
                
                # Deduct fees from distribution
                distribution_amounts[i] -= accumulated_fees
            else:
                management_fee_amounts[i] = 0
            
            # Reduce cost basis by the proportional amount of original investment
            # Only the portion of cost basis corresponding to shares being sold
            cost_basis_reduction = initial_cost_basis * distribution_percentage
            remaining_cost_basis -= cost_basis_reduction
            
            # Ensure cost basis doesn't go negative
            remaining_cost_basis = max(0, remaining_cost_basis)
            
            # Update last fee calculation date
            last_fee_calc_date = distribution_date
    
    # Apply Capital Call Fees (only on first exit, deducted from first distribution)
    if capital_call_fees > 0 and is_first_exit:
        remaining_cc_fees = capital_call_fees
        for i in range(len(distribution_amounts)):
            if remaining_cc_fees <= 0:
                break
            if distribution_amounts[i] >= remaining_cc_fees:
                distribution_amounts[i] -= remaining_cc_fees
                capital_call_fee_amounts[i] = remaining_cc_fees
                remaining_cc_fees = 0
            else:
                capital_call_fee_amounts[i] = distribution_amounts[i]
                remaining_cc_fees -= distribution_amounts[i]
                distribution_amounts[i] = 0
    
    # Apply Priority Return (only on first exit, deducted from first distribution)
    if priority_return > 0 and is_first_exit:
        remaining_pr = priority_return
        for i in range(len(distribution_amounts)):
            if remaining_pr <= 0:
                break
            if distribution_amounts[i] >= remaining_pr:
                distribution_amounts[i] -= remaining_pr
                priority_return_amounts[i] = remaining_pr
                remaining_pr = 0
            else:
                priority_return_amounts[i] = distribution_amounts[i]
                remaining_pr -= distribution_amounts[i]
                distribution_amounts[i] = 0
    
    # Calculate total fee amounts for each distribution
    total_fee_amounts = [
        mgmt + cc + pr for mgmt, cc, pr in 
        zip(management_fee_amounts, capital_call_fee_amounts, priority_return_amounts)
    ]
    
    # Update schedule with detailed fee breakdown
    schedule['distribution_amounts_after_fees'] = distribution_amounts
    schedule['management_fee_amounts'] = management_fee_amounts
    schedule['capital_call_fee_amounts'] = capital_call_fee_amounts 
    schedule['priority_return_amounts'] = priority_return_amounts
    schedule['total_fee_amounts'] = total_fee_amounts
    schedule['fee_amounts'] = total_fee_amounts  # Legacy compatibility
    schedule['total_management_fees'] = management_fees
    schedule['total_capital_call_fees'] = capital_call_fees
    schedule['total_priority_return'] = priority_return
    schedule['total_fees'] = management_fees + capital_call_fees + priority_return
    schedule['is_first_exit'] = is_first_exit
    
    return schedule

def create_ipo_visualization(ipo_schedules):
    """
    Create a visualization of IPO distributions over time.
    
    Args:
        ipo_schedules: List of IPO distribution schedules
    
    Returns:
        Plotly figure object
    """
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    
    # Create figure with secondary y-axis
    fig = make_subplots(
        rows=1, cols=1,
        specs=[[{"secondary_y": True}]]
    )
    
    # Add distribution bars for each company
    for schedule in ipo_schedules:
        company_name = schedule['company_name']
        dates = schedule['distribution_dates']
        amounts = schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts'])
        
        # Add bar trace
        fig.add_trace(
            go.Bar(
                x=dates,
                y=amounts,
                name=company_name,
                text=[f"${amt:,.0f}" for amt in amounts],
                textposition='outside',
                hovertemplate='%{x}<br>%{text}<extra></extra>'
            ),
            secondary_y=False
        )
    
    # Update layout
    fig.update_layout(
        title="IPO Distribution Timeline",
        xaxis_title="Distribution Date",
        yaxis_title="Distribution Amount ($)",
        barmode='group',
        showlegend=True,
        height=400,
        hovermode='x unified'
    )
    
    # Format y-axis
    fig.update_yaxes(tickformat='$,.0f', secondary_y=False)
    
    return fig

def apply_fees_to_ipo_distributions(schedule, total_fees):
    """
    Legacy function for backward compatibility.
    Apply management fees to IPO distributions using decremental allocation.
    
    Args:
        schedule: IPO distribution schedule
        total_fees: Total management fees to allocate
    
    Returns:
        Modified schedule with fees applied
    """
    # Convert to enhanced format for processing
    fee_entry = {
        'Management Fees': total_fees,
        'Capital Call Fees': 0,
        'Priority Return': 0
    }
    
    return apply_enhanced_fees_to_ipo_distributions(schedule, fee_entry, is_first_exit=False)
