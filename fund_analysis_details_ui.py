import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from financial_calculations import xirr, aggregate_cashflows_by_date, get_capital_call_data

# Gross Invested Capital mapping based on the provided table
GROSS_INVESTED_CAPITAL = {
    'Gainwell': 1200000000,  # (1,200,000,000)
    'Peraton': 1625000000,    # (1,625,000,000)
    'Cubic': *********,       # (785,000,000)
    'Finalsite': *********,   # (555,000,000)
    'HMH': 1180000000,        # (1,180,000,000)
    'Coronis': *********      # (540,000,000)
}

# Company name mapping for variations in data
COMPANY_NAME_MAPPING = {
    'Gainwell Technologies, LLC': '<PERSON>ainwell',
    'Gainwell Topco Holdings L.P.': 'Gainwell',
    'Gainwell Technologies': 'Gainwell',
    'Peraton Corp.': 'Peraton',
    'Peraton Holdings II L.P.': 'Peraton',
    'Peraton Inc': 'Peraton',
    'Peraton Inc.': 'Peraton',
    'Atlas Topco Holdings L.P.': 'Cubic',
    'Cubic Corporation': 'Cubic',
    'Finalsite Holdings, Inc': 'Finalsite',
    'Finalsite Holdings, Inc.': 'Finalsite',
    'Finalsite Inc': 'Finalsite',
    'Finalsite Inc.': 'Finalsite',
    'Harbor Topco Holdings L.P.': 'HMH',
    'HMH Topco Holdings L.P.': 'HMH',
    'HMH Topco Holdings L.P. (NWEA)': 'HMH',
    'NWEA': 'HMH',
    'Houghton Mifflin Harcourt': 'HMH',
    'HMH Holdings': 'HMH',
    'HMH Company': 'HMH',
    'Coronis Health, LLC': 'Coronis'
}

def get_gross_invested_capital(company_name):
    """
    Get the gross invested capital for a company, handling name variations.
    Returns the gross amount (positive value) or 0 if not found.
    """
    if company_name is None:
        return 0
    
    # Map company name to standard name
    standard_name = COMPANY_NAME_MAPPING.get(company_name, company_name)
    
    # Return gross invested capital (positive value)
    return GROSS_INVESTED_CAPITAL.get(str(standard_name), 0)

from capital_call_fees import calculate_capital_call_fees_at_exit, load_capital_call_fees

def display_details_section(
    fee_tracker, exit_dates, fee_balance, fee_date, q_fee, company_fees, fee_mapping, 
    include_capital_calls, exit_timeline, company_net_investments, company_book_values, 
    carry_calculation, hurdle_breakdown_df, ipo_schedules=None, edited_df=None, data_df=None
):
    """
    Displays the expandable section with detailed fee and exit information.
    """
    show_exit_details = st.checkbox("📊 Show Source Information", value=False, help="Toggle to show/hide additional source information")
    
    if show_exit_details and fee_tracker:
        fee_df = pd.DataFrame(fee_tracker)
        st.subheader("Exit and Fee Details")
        
        with st.expander("📅 Show Fee Accrual Timeline", expanded=False):
            st.subheader("Fee Accrual Timeline")
            
            timeline_data = []
            sorted_exit_dates_list = sorted(exit_dates.keys())
            
            if sorted_exit_dates_list:
                current_fee_balance_tl = fee_balance
                current_fee_date_tl = fee_date
                exit_num_tl = 0
                current_quarterly_fee_tl = q_fee
                cumulative_mgmt_fees_tl = 0

                for i_tl, exit_date_tl in enumerate(sorted_exit_dates_list):
                    exit_num_tl += 1
                    is_first_exit_tl = (exit_num_tl == 1)
                    
                    companies_exiting_tl_list = [ei["Company"] for ei in exit_dates[exit_date_tl]]
                    
                    days_in_period_tl = (exit_date_tl - current_fee_date_tl).days
                    quarters_in_period_tl = days_in_period_tl / 91.25
                    new_mgmt_fees_accrued_tl = current_quarterly_fee_tl * quarters_in_period_tl

                    capital_call_amount_tl = 0
                    capital_call_pr_tl = 0
                    priority_return_tl = 0

                    if include_capital_calls and is_first_exit_tl:
                        capital_call_data_tl = get_capital_call_data()
                        capital_call_results_tl = calculate_capital_call_fees_at_exit(
                            capital_call_data_tl, exit_date_tl, is_first_exit_tl, hurdle_rate=0.08
                        )
                        capital_call_amount_tl = capital_call_results_tl['total_satisfied_amount']
                        capital_call_pr_tl = capital_call_results_tl['total_satisfied_pr']
                        priority_return_tl = capital_call_pr_tl

                    total_fees_due_tl = current_fee_balance_tl + new_mgmt_fees_accrued_tl + capital_call_amount_tl + capital_call_pr_tl
                    period_label_tl = "Initial Balance to First Exit" if i_tl == 0 else f"Exit {i_tl} to Exit {i_tl+1}"

                    timeline_data.append({
                        "Period": period_label_tl, "Start Date": current_fee_date_tl, "End Date": exit_date_tl,
                        "Days": days_in_period_tl, "Starting Balance": current_fee_balance_tl,
                        "New Mgmt Fees": new_mgmt_fees_accrued_tl, "Mgmt Fee Total": cumulative_mgmt_fees_tl + new_mgmt_fees_accrued_tl,
                        "Capital Call Fees": capital_call_amount_tl, "Capital Call PR (8%)": capital_call_pr_tl,
                        "Priority Return": priority_return_tl, "Total Fees Due": total_fees_due_tl,
                        "Companies Exiting": ", ".join(companies_exiting_tl_list), "Is First Exit": is_first_exit_tl
                    })

                    cumulative_mgmt_fees_tl += new_mgmt_fees_accrued_tl
                    
                    exiting_companies_q_fees_tl = sum(company_fees.get(fee_mapping.get(c, c), 0) for c in companies_exiting_tl_list)
                    current_quarterly_fee_tl = max(0, current_quarterly_fee_tl - exiting_companies_q_fees_tl)

                    current_fee_date_tl = exit_date_tl
                    current_fee_balance_tl = 0

                if timeline_data:
                    fee_timeline_df = pd.DataFrame(timeline_data)
                    format_dict = {
                        "Start Date": lambda x: x.strftime('%Y-%m-%d'), "End Date": lambda x: x.strftime('%Y-%m-%d'),
                        "Days": "{:,.0f}", "Starting Balance": "${:,.0f}", "New Mgmt Fees": "${:,.0f}",
                        "Mgmt Fee Total": "${:,.0f}", "Capital Call Fees": "${:,.0f}", "Priority Return": "${:,.0f}",
                        "Total Fees Due": "${:,.0f}"
                    }
                    st.table(fee_timeline_df[[c for c in fee_timeline_df.columns if c != "Capital Call PR (8%)"]].style.format(format_dict))
                    
        with st.expander("📊 Show Capital Call Fee Details", expanded=False):
            first_exit_date = exit_timeline['Exit Date'].dropna().min()
            
            if pd.isna(first_exit_date):
                st.warning("No exit dates to calculate capital call fees.")
            else:
                capital_call_data = load_capital_call_fees()
                capital_call_results = calculate_capital_call_fees_at_exit(capital_call_data, pd.to_datetime(first_exit_date), True)
                total_capital_call_fees = capital_call_results['total_satisfied_amount'] + capital_call_results['total_satisfied_pr']

                st.table(exit_timeline[['Company', 'MOIC', 'Exit Date']].style.format({'MOIC': '{:.2f}x'}))
                st.divider()
                
                col1, col2, col3 = st.columns(3)
                col1.metric("Capital Call Principal", f"${capital_call_results['total_satisfied_amount']:,.0f}")
                col2.metric("Priority Return (8%)", f"${capital_call_results['total_satisfied_pr']:,.0f}")
                col3.metric("TOTAL Capital Call Fees", f"${total_capital_call_fees:,.0f}")

                details = [{'Call Type': c['call_type'], 'Call Date': c['call_date'].strftime('%Y-%m-%d'),
                            'Principal': f"${c['amount']:,.0f}", 'Days to Exit': c['days_to_exit'],
                            'Years to Exit': f"{c['years_to_exit']:.3f}", 'Priority Return': f"${c['calculated_pr']:,.0f}",
                            'Total': f"${c['amount'] + c['calculated_pr']:,.0f}"} for c in capital_call_results['satisfied_calls']]
                details.append({'Call Type': '**TOTAL**', 'Principal': f"**${capital_call_results['total_satisfied_amount']:,.0f}**",
                                'Priority Return': f"**${capital_call_results['total_satisfied_pr']:,.0f}**",
                                'Total': f"**${total_capital_call_fees:,.0f}**"})
                st.table(pd.DataFrame(details))

        # Add IPO Company Management Fees by Distribution section
        with st.expander("📊 IPO Company Management Fees by Distribution", expanded=False):
            # Check if IPO schedules are available in session state
            ipo_schedules = st.session_state.get('ipo_schedules', [])

            if ipo_schedules:
                ipo_fee_data = []

                for schedule in ipo_schedules:
                    company_name = schedule['company_name']

                    # Check if this schedule has fee breakdown data
                    if 'management_fee_amounts' in schedule:
                        distribution_dates = schedule['distribution_dates']
                        mgmt_fee_amounts = schedule.get('management_fee_amounts', [])
                        cc_fee_amounts = schedule.get('capital_call_fee_amounts', [])
                        pr_amounts = schedule.get('priority_return_amounts', [])
                        gross_amounts = schedule['distribution_amounts']
                        net_amounts = schedule.get('distribution_amounts_after_fees', gross_amounts)

                        for i, (date, gross_amt, net_amt) in enumerate(zip(distribution_dates, gross_amounts, net_amounts)):
                            mgmt_fee = mgmt_fee_amounts[i] if i < len(mgmt_fee_amounts) else 0
                            cc_fee = cc_fee_amounts[i] if i < len(cc_fee_amounts) else 0
                            pr_fee = pr_amounts[i] if i < len(pr_amounts) else 0
                            total_fee = mgmt_fee + cc_fee + pr_fee

                            # Calculate fee rate as percentage of gross distribution
                            fee_rate = (total_fee / gross_amt * 100) if gross_amt > 0 else 0

                            ipo_fee_data.append({
                                'Company': company_name,
                                'Distribution #': i + 1,
                                'Date': date.strftime('%Y-%m-%d'),
                                'Gross Distribution': f"${gross_amt:,.0f}",
                                'Management Fees': f"${mgmt_fee:,.0f}" if mgmt_fee > 0 else "-",
                                'Capital Call Fees': f"${cc_fee:,.0f}" if cc_fee > 0 else "-",
                                'Priority Return': f"${pr_fee:,.0f}" if pr_fee > 0 else "-",
                                'Total Fees': f"${total_fee:,.0f}" if total_fee > 0 else "-",
                                'Net to LPs': f"${net_amt:,.0f}",
                                'Fee Rate %': f"{fee_rate:.2f}%" if fee_rate > 0 else "-"
                            })
                    else:
                        # If no fee breakdown available, show basic info
                        distribution_dates = schedule['distribution_dates']
                        gross_amounts = schedule['distribution_amounts']

                        for i, (date, gross_amt) in enumerate(zip(distribution_dates, gross_amounts)):
                            ipo_fee_data.append({
                                'Company': company_name,
                                'Distribution #': i + 1,
                                'Date': date.strftime('%Y-%m-%d'),
                                'Gross Distribution': f"${gross_amt:,.0f}",
                                'Management Fees': "Not calculated",
                                'Capital Call Fees': "Not calculated",
                                'Priority Return': "Not calculated",
                                'Total Fees': "Not calculated",
                                'Net to LPs': f"${gross_amt:,.0f}",
                                'Fee Rate %': "N/A"
                            })

                if ipo_fee_data:
                    ipo_fee_df = pd.DataFrame(ipo_fee_data)
                    st.dataframe(ipo_fee_df, use_container_width=True)

                    # Calculate summary for IPO companies with fee data
                    companies_with_fees = [row for row in ipo_fee_data if row['Management Fees'] != "Not calculated"]

                    if companies_with_fees:
                        total_ipo_mgmt_fees = sum([
                            float(row['Management Fees'].replace('$', '').replace(',', ''))
                            for row in companies_with_fees if row['Management Fees'] != '-'
                        ])
                        total_ipo_cc_fees = sum([
                            float(row['Capital Call Fees'].replace('$', '').replace(',', ''))
                            for row in companies_with_fees if row['Capital Call Fees'] != '-'
                        ])
                        total_ipo_pr = sum([
                            float(row['Priority Return'].replace('$', '').replace(',', ''))
                            for row in companies_with_fees if row['Priority Return'] != '-'
                        ])
                        total_ipo_gross = sum([
                            float(row['Gross Distribution'].replace('$', '').replace(',', ''))
                            for row in ipo_fee_data
                        ])

                        st.markdown("**IPO Companies Fee Summary:**")
                        summary_cols = st.columns(5)
                        with summary_cols[0]:
                            st.metric("Total Gross Distributions", f"${total_ipo_gross:,.0f}")
                        with summary_cols[1]:
                            st.metric("Management Fees", f"${total_ipo_mgmt_fees:,.0f}")
                        with summary_cols[2]:
                            st.metric("Capital Call Fees", f"${total_ipo_cc_fees:,.0f}")
                        with summary_cols[3]:
                            st.metric("Priority Return", f"${total_ipo_pr:,.0f}")
                        with summary_cols[4]:
                            total_ipo_fees = total_ipo_mgmt_fees + total_ipo_cc_fees + total_ipo_pr
                            st.metric("Total Fees", f"${total_ipo_fees:,.0f}")

                        # Show effective fee rate
                        if total_ipo_gross > 0:
                            effective_fee_rate = (total_ipo_fees / total_ipo_gross) * 100
                            st.info(f"💡 **Effective Fee Rate on IPO Distributions**: {effective_fee_rate:.2f}%")
                    else:
                        st.info("💡 Fee calculations not available for IPO distributions. Enable fee calculations in IPO scenario settings.")
                else:
                    st.info("No IPO distribution data available for fee analysis")
            else:
                st.info("No IPO companies selected. Configure IPO scenario to see management fees by distribution.")

        fee_df["Net Investment"] = fee_df["Company"].apply(lambda x: company_net_investments.get(x.replace(" (Written Off)", ""), 0))
        fee_df["Book Value"] = fee_df["Company"].apply(lambda x: company_book_values.get(x.replace(" (Written Off)", ""), 0))

        if include_capital_calls:
            if 'Capital Call PR' in fee_df.columns and 'Priority Return' not in fee_df.columns:
                fee_df = fee_df.rename(columns={'Capital Call PR': 'Priority Return'})
            elif 'Capital Call PR' in fee_df.columns:
                 fee_df = fee_df.drop(columns=['Capital Call PR'])
            cols = ["Company", "Net Investment", "MOIC", "Exit Date", "Exit Value", "Management Fees", "Capital Call Fees", "Priority Return", "Total Fees", "Status", "Is First Exit"]
        else:
            cols = ["Company", "Net Investment", "MOIC", "Exit Date", "Exit Value", "Management Fees", "Total Fees", "Status"]
        
        display_cols = [c for c in cols if c in fee_df.columns]
        
        if 'capital_call_results' in locals() and include_capital_calls:
            st.info(f"First company pays all management fees and ${total_capital_call_fees:,.0f} in capital call fees.")
        
        format_dict = {"Net Investment": "${:,.0f}", "MOIC": "{:.2f}x", "Exit Value": "${:,.0f}",
                       "Management Fees": "${:,.0f}", "Capital Call Fees": "${:,.0f}", "Total Fees": "${:,.0f}", "Priority Return": "${:,.0f}"}
        st.table(fee_df[display_cols].style.format({k:v for k,v in format_dict.items() if k in display_cols}))
        
        st.subheader("GP Carry Calculation")
        
        total_exit_value = sum(item.get("Exit Value", 0) for item in fee_tracker)
        total_mgmt_fees = sum(item.get("Management Fees", 0) for item in fee_tracker)
        distributable_value = total_exit_value - total_mgmt_fees
        
        total_capital_call_fees = sum(item.get("Capital Call Fees", 0) for item in fee_tracker)
        total_calls = sum(company_net_investments.values()) + total_capital_call_fees
        
        total_capital_call_pr = sum(item.get("Capital Call PR", 0) for item in fee_tracker)
        total_priority_return = carry_calculation['hurdle_return'] + total_capital_call_pr
        
        remaining_after_calls = distributable_value - total_calls - total_priority_return
        gp_catchup = max(0, min(remaining_after_calls, (total_priority_return / 0.8) * 0.2))
        gp_carry = max(0, remaining_after_calls - gp_catchup) * 0.20
        
        col1, col2, col3, col4, col5 = st.columns(5)
        col1.metric("Total Profit", f"${distributable_value - total_calls:,.2f}")
        col2.metric("Priority Return", f"${total_priority_return:,.2f}")
        col3.metric("Remaining", f"${remaining_after_calls:,.2f}")
        col4.metric("GP Catchup", f"${gp_catchup:,.2f}")
        col5.metric("GP Carry (20%)", f"${gp_carry:,.2f}")
        st.metric("**Total GP Compensation**", f"${gp_catchup + gp_carry:,.2f}")

        if not hurdle_breakdown_df.empty:
            with st.expander("📊 Time-Weighted Hurdle Return Breakdown", expanded=False):
                st.table(hurdle_breakdown_df.style.format({'Investment_Amount': '${:,.0f}', 'Hurdle_Return': '${:,.0f}', 'Days_Held': '{:,.0f}', 'Years_Held': '{:.2f}'}))
        
    elif show_exit_details:
        st.info("No exit data available.")
    
    # Always show Gross IRR Cash Flows section (outside of conditional checks)
    with st.expander("📈 Gross IRR Cash Flows", expanded=False):
        st.markdown("##### Gross IRR Cash Flows")
        st.caption("Showing the underlying cash flows for IRR calculation, formatted to match Excel example")
        _display_gross_irr_cash_flows_in_details(ipo_schedules, edited_df, company_net_investments, data_df)
    
    # Always show Net IRR Cash Flows section 
    with st.expander("📉 Net IRR Cash Flows", expanded=False):
        st.markdown("##### Net IRR Cash Flows (After Fees)")
        st.caption("Showing cash flows after deducting management fees and other costs")
        _display_net_irr_cash_flows_in_details(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
    
 

def _display_gross_irr_cash_flows_in_details(ipo_schedules, edited_df, company_net_investments, data_df=None):
    """
    Display gross IRR cash flows using dynamic cash flow generation from actual data.
    Uses real historical data from data_df and projected exits from edited_df.
    """
    try:
        import pandas as pd
        from datetime import datetime
        from financial_calculations import xirr
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            st.info("No data available for gross IRR calculation.")
            return
        
        fund_name = "Veritas Capital Fund VII"
        
        # Generate historical cash flows from actual data (following codebase pattern)
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Build fund cash flows starting with historical data
        fund_flows = historical_flows.copy()
        all_cash_flows_for_display = []
        
        # Add historical cash flows to display
        for date, amount in historical_flows:
            if amount != 0:  # Only show non-zero cash flows
                # Find the company for this cash flow
                matching_rows = data_df[data_df["Date"] == date]
                company_name = "Multiple Companies" if len(matching_rows) > 1 else (matching_rows.iloc[0]["Deal Name"] if not matching_rows.empty else "Unknown")
                
                all_cash_flows_for_display.append({
                    'Fund Name': fund_name,
                    'Deal Name': company_name,
                    'Realized/Unrealized': 'Historical',
                    'Date': pd.to_datetime(date).strftime('%m/%d/%Y'),
                    'Cash Flows': f"({abs(amount):,.0f})" if amount < 0 else f"{amount:,.0f}"
                })
        
        # Process projected exits from edited_df (following codebase pattern)
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Process each exit date
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            exit_value_total = 0
            
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                # Use gross invested capital instead of net investment for Gross IRR
                gross_investment = get_gross_invested_capital(company_name)
                
                if gross_investment > 0:
                    exit_value = gross_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name in ipo_company_map:
                        # Skip the single exit - it will be replaced by IPO distributions
                        continue
                    else:
                        # Regular exit
                        exit_value_total += exit_value
                        
                        # Add to display
                        all_cash_flows_for_display.append({
                            'Fund Name': fund_name,
                            'Deal Name': company_name,
                            'Realized/Unrealized': 'Projected',
                            'Date': exit_date_proc.strftime('%m/%d/%Y'),
                            'Cash Flows': f"{exit_value:,.0f}"
                        })
            
            # Add total exit value to fund flows (excluding IPO companies)
            if exit_value_total > 0:
                fund_flows.append((exit_date_proc, exit_value_total))
        
        # Add IPO distributions if any
        if ipo_schedules:
            for schedule in ipo_schedules:
                for i, (dist_date, dist_amount) in enumerate(zip(
                    schedule['distribution_dates'], 
                    schedule['distribution_amounts']
                )):
                    # Add IPO distribution to display
                    all_cash_flows_for_display.append({
                        'Fund Name': fund_name,
                        'Deal Name': f"{company_name} (IPO Dist {i+1})",
                        'Realized/Unrealized': 'IPO Distribution',
                        'Date': dist_date.strftime('%m/%d/%Y'),
                        'Cash Flows': f"{dist_amount:,.0f}"
                    })
                    
                    # Add to fund flows
                    fund_flows.append((dist_date, dist_amount))
        
        # Add remaining companies at their current book value (following codebase pattern)
        companies_with_exits = set(exit_timeline["Company"].tolist())
        
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                gross_investment_rem = get_gross_invested_capital(company_rem)
                if gross_investment_rem > 0:
                    # Get the latest date for this company
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        latest_date_rem = comp_data_rem["Date"].max()
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        
                        if not book_row.empty:
                            book_value = book_row.iloc[0]["Book Value"]
                            if book_value > 1:  # Only add if book value is greater than 1 to avoid near-zero values
                                fund_flows.append((latest_date_rem, book_value))
                                
                                # Add to display
                                all_cash_flows_for_display.append({
                                    'Fund Name': fund_name,
                                    'Deal Name': company_rem,
                                    'Realized/Unrealized': 'Unrealized',
                                    'Date': pd.to_datetime(latest_date_rem).strftime('%m/%d/%Y'),
                                    'Cash Flows': f"{book_value:,.0f}"
                                })
        
        # Calculate Gross IRR using dynamic cash flows
        try:
            from financial_calculations import aggregate_cashflows_by_date
            fund_flows_agg = aggregate_cashflows_by_date(fund_flows)
            overall_irr = xirr(fund_flows_agg)
            gross_irr_pct = overall_irr * 100 if overall_irr is not None else 0
        except Exception as e:
            gross_irr_pct = 0
            st.error(f"Error calculating IRR: {str(e)}")
        
        # Calculate Gross MOIC (Projected Values / Total Historical Cash Flows)
        try:
            # Total historical cash flows (net invested capital: contributions minus early distributions)
            total_historical_cash_flows = abs(sum(amount for date, amount in historical_flows))
            
            # Projected values (exit values, IPO distributions, unrealized book values)
            projected_values = 0
            
            # Add exit values from non-IPO companies
            for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
                for exit_info in companies_exiting:
                    company_name = exit_info["Company"]
                    moic = exit_info["MOIC"]
                    gross_investment = get_gross_invested_capital(company_name)
                    
                    if gross_investment > 0:
                        exit_value = gross_investment * moic
                        
                        # Check if this company is in IPO scenario
                        if company_name not in ipo_company_map:
                            projected_values += exit_value
            
            # Add IPO distributions if any
            if ipo_schedules:
                for schedule in ipo_schedules:
                    projected_values += sum(schedule['distribution_amounts'])
            
            # Add remaining companies at their current book value
            companies_with_exits = set(exit_timeline["Company"].tolist())
            for company_rem in company_net_investments.keys():
                if company_rem not in companies_with_exits:
                    gross_investment_rem = get_gross_invested_capital(company_rem)
                    if gross_investment_rem > 0:
                        comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                        if not comp_data_rem.empty:
                            book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                            if not book_row.empty:
                                projected_values += book_row.iloc[0]["Book Value"]
            
            gross_moic = projected_values / total_historical_cash_flows if total_historical_cash_flows > 0 else 0
            
        except Exception as e:
            gross_moic = 0
            st.error(f"Error calculating MOIC: {str(e)}")
        
        # Add blank separator row
        all_cash_flows_for_display.append({
            'Fund Name': '',
            'Deal Name': '',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': ''
        })
        
        # Add overall Gross IRR row at the bottom
        all_cash_flows_for_display.append({
            'Fund Name': fund_name,
            'Deal Name': 'Gross IRR',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': f"{gross_irr_pct:.2f}%"
        })
        
        # Add overall Gross MOIC row at the bottom
        all_cash_flows_for_display.append({
            'Fund Name': fund_name,
            'Deal Name': 'Gross MOIC',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': f"{gross_moic:.2f}x"
        })
        
        # Create DataFrame
        df = pd.DataFrame(all_cash_flows_for_display)
        
        # Sort by date (oldest to newest), but keep summary rows at the bottom
        summary_rows = df[df['Date'] == ''].copy()  # Blank and summary rows
        data_rows = df[df['Date'] != ''].copy()    # Actual cash flow rows
        
        # Sort data rows by date
        if not data_rows.empty:
            data_rows['Date_Sort'] = pd.to_datetime(data_rows['Date'], format='%m/%d/%Y', errors='coerce')
            data_rows = data_rows.sort_values('Date_Sort').drop('Date_Sort', axis=1)
        
        # Combine sorted data rows with summary rows
        df = pd.concat([data_rows, summary_rows], ignore_index=True)
        
        # Display the table
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Fund Name": st.column_config.TextColumn("Fund Name", width="medium"),
                "Deal Name": st.column_config.TextColumn("Deal Name", width="medium"),
                "Realized/Unrealized": st.column_config.TextColumn("Realized/Unrealized", width="medium"),
                "Date": st.column_config.TextColumn("Date", width="medium"),
                "Cash Flows": st.column_config.TextColumn("Cash Flows", width="medium")
            }
        )
        
        # Display summary info with IPO scenario indicator
        summary_text = f"📊 **Dynamically Calculated Gross IRR: {gross_irr_pct:.2f}%** | **Gross MOIC: {gross_moic:.2f}x** (Based on actual data + user scenarios)"
        if ipo_schedules:
            ipo_companies = [s['company_name'] for s in ipo_schedules]
            summary_text += f"\n\n🔄 **IPO Scenario Active** for: {', '.join(ipo_companies)}"
        st.info(summary_text)
        
    except Exception as e:
        st.error(f"Error displaying gross IRR cash flows: {str(e)}")
        import traceback
        print(f"Gross IRR cash flows error: {traceback.format_exc()}")


def _display_net_irr_cash_flows_in_details(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee):
    """
    Display net IRR cash flows using dynamic cash flow generation after deducting fees.
    Uses real historical data and projected exits minus management fees and other costs.
    """
    try:
        import pandas as pd
        from datetime import datetime
        from financial_calculations import xirr, aggregate_cashflows_by_date, calculate_management_fees_with_hurdle
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            st.info("No data available for net IRR calculation.")
            return
        
        fund_name = "Veritas Capital Fund VII"
        
        # Generate historical cash flows from actual data (same as gross)
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Build fund cash flows starting with historical data
        fund_flows_net = historical_flows.copy()
        all_cash_flows_for_display_net = []
        
        # Add historical cash flows to display
        for date, amount in historical_flows:
            if amount != 0:  # Only show non-zero cash flows
                # Find the company for this cash flow
                matching_rows = data_df[data_df["Date"] == date]
                company_name = "Multiple Companies" if len(matching_rows) > 1 else (matching_rows.iloc[0]["Deal Name"] if not matching_rows.empty else "Unknown")
                
                all_cash_flows_for_display_net.append({
                    'Fund Name': fund_name,
                    'Deal Name': company_name,
                    'Realized/Unrealized': 'Historical',
                    'Date': pd.to_datetime(date).strftime('%m/%d/%Y'),
                    'Cash Flows': f"({abs(amount):,.0f})" if amount < 0 else f"{amount:,.0f}"
                })
        
        # Process exits with fees (using fee_tracker data)
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date and calculate fees
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Track whether we've processed first exit (for fee calculation)
        first_exit_processed = False
        remaining_fee_balance = fee_balance
        last_fee_date = fee_date
        
        # Process each exit date with fees
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            # Calculate fees for this exit period
            is_first_exit = not first_exit_processed
            
            # Calculate management fees for this period
            fee_details = calculate_management_fees_with_hurdle(
                last_fee_date, exit_date_proc, remaining_fee_balance, q_fee,
                hurdle_rate=0.08,
                include_capital_calls=True,
                is_first_exit=is_first_exit
            )
            
            total_period_fees = fee_details['total_fees']
            mgmt_fees_only = fee_details.get('mgmt_fees_only', 0)
            capital_call_fees = fee_details.get('capital_call_amount', 0)
            capital_call_pr = fee_details.get('capital_call_pr', 0)
            
            # Calculate total exit value for this period
            period_exit_value = 0
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                net_investment = company_net_investments.get(company_name, 0)
                
                if net_investment > 0:
                    exit_value = net_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name in ipo_company_map:
                        # Skip the single exit - it will be replaced by IPO distributions
                        continue
                    else:
                        # Regular exit
                        period_exit_value += exit_value
                        
                        # Add gross exit value to display
                        all_cash_flows_for_display_net.append({
                            'Fund Name': fund_name,
                            'Deal Name': company_name,
                            'Realized/Unrealized': 'Projected Exit',
                            'Date': exit_date_proc.strftime('%m/%d/%Y'),
                            'Cash Flows': f"{exit_value:,.0f}"
                        })
            
            # Add fee payments as negative cash flows
            if mgmt_fees_only > 0:
                all_cash_flows_for_display_net.append({
                    'Fund Name': fund_name,
                    'Deal Name': 'Management Fees',
                    'Realized/Unrealized': 'Fee Payment',
                    'Date': exit_date_proc.strftime('%m/%d/%Y'),
                    'Cash Flows': f"({mgmt_fees_only:,.0f})"
                })
            
            if capital_call_fees > 0:
                all_cash_flows_for_display_net.append({
                    'Fund Name': fund_name,
                    'Deal Name': 'Capital Call Fees',
                    'Realized/Unrealized': 'Fee Payment',
                    'Date': exit_date_proc.strftime('%m/%d/%Y'),
                    'Cash Flows': f"({capital_call_fees:,.0f})"
                })
            
            if capital_call_pr > 0:
                all_cash_flows_for_display_net.append({
                    'Fund Name': fund_name,
                    'Deal Name': 'Capital Call PR (8%)',
                    'Realized/Unrealized': 'Fee Payment',
                    'Date': exit_date_proc.strftime('%m/%d/%Y'),
                    'Cash Flows': f"({capital_call_pr:,.0f})"
                })
            
            # Add net exit value (after fees) to fund flows
            net_exit_value = period_exit_value - total_period_fees
            if net_exit_value > 0:
                fund_flows_net.append((exit_date_proc, net_exit_value))
                
                # Add net exit summary to display
                all_cash_flows_for_display_net.append({
                    'Fund Name': fund_name,
                    'Deal Name': f'Net Exit Value (After ${total_period_fees:,.0f} fees)',
                    'Realized/Unrealized': 'Net Result',
                    'Date': exit_date_proc.strftime('%m/%d/%Y'),
                    'Cash Flows': f"{net_exit_value:,.0f}"
                })
            
            # Update for next period
            first_exit_processed = True
            remaining_fee_balance = 0  # Fees paid, balance reset
            last_fee_date = exit_date_proc
        
        # Add IPO distributions if any schedules exist
        if ipo_schedules:
            for schedule in ipo_schedules:
                company_name = schedule['company_name']
                distribution_dates = schedule['distribution_dates']
                distribution_amounts = schedule['distribution_amounts']
                
                for i, (dist_date, dist_amount) in enumerate(zip(distribution_dates, distribution_amounts)):
                    # Calculate fees for this IPO distribution
                    dist_date = pd.to_datetime(dist_date)
                    
                    # For IPO distributions, calculate fees (simplified approach)
                    # This is approximate - real implementation would need more complex fee tracking
                    fee_ratio = 0.02  # Approximate management fee ratio
                    net_distribution = dist_amount * (1 - fee_ratio)
                    
                    # Add gross distribution to display
                    all_cash_flows_for_display_net.append({
                        'Fund Name': fund_name,
                        'Deal Name': f"{company_name} (IPO Dist {i+1})",
                        'Realized/Unrealized': 'IPO Distribution',
                        'Date': dist_date.strftime('%m/%d/%Y'),
                        'Cash Flows': f"{dist_amount:,.0f}"
                    })
                    
                    # Add fee deduction
                    if dist_amount * fee_ratio > 0:
                        all_cash_flows_for_display_net.append({
                            'Fund Name': fund_name,
                            'Deal Name': f"Management Fees (IPO Dist {i+1})",
                            'Realized/Unrealized': 'Fee Payment',
                            'Date': dist_date.strftime('%m/%d/%Y'),
                            'Cash Flows': f"({dist_amount * fee_ratio:,.0f})"
                        })
                    
                    # Add net distribution to fund flows
                    fund_flows_net.append((dist_date, net_distribution))
        
        # Add remaining companies at their current book value (same as gross)
        companies_with_exits = set(exit_timeline["Company"].tolist())
        
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                net_investment_rem = company_net_investments.get(company_rem, 0)
                if net_investment_rem > 0:
                    # Get the latest date for this company
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        latest_date_rem = comp_data_rem["Date"].max()
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        
                        if not book_row.empty:
                            book_value = book_row.iloc[0]["Book Value"]
                            fund_flows_net.append((latest_date_rem, book_value))
                            
                            # Add to display
                            all_cash_flows_for_display_net.append({
                                'Fund Name': fund_name,
                                'Deal Name': company_rem,
                                'Realized/Unrealized': 'Unrealized',
                                'Date': pd.to_datetime(latest_date_rem).strftime('%m/%d/%Y'),
                                'Cash Flows': f"{book_value:,.0f}"
                            })
        
        # Calculate Net IRR using dynamic cash flows after fees
        try:
            fund_flows_net_agg = aggregate_cashflows_by_date(fund_flows_net)
            net_irr = xirr(fund_flows_net_agg)
            net_irr_pct = net_irr * 100 if net_irr is not None else 0
        except Exception as e:
            net_irr_pct = 0
            st.error(f"Error calculating Net IRR: {str(e)}")
        
        # Calculate Net MOIC (Net Projected Values / Total Historical Cash Flows)
        try:
            # Total historical cash flows (net invested capital: contributions minus early distributions)
            total_historical_cash_flows_net = abs(sum(amount for date, amount in historical_flows))
            
            # Calculate projected values and fees using actual fee tracker data
            projected_values = 0
            
            # Add exit values from non-IPO companies
            for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
                for exit_info in companies_exiting:
                    company_name = exit_info["Company"]
                    moic = exit_info["MOIC"]
                    net_investment = company_net_investments.get(company_name, 0)
                    
                    if net_investment > 0:
                        exit_value = net_investment * moic
                        
                        # Check if this company is in IPO scenario
                        if company_name not in ipo_company_map:
                            projected_values += exit_value
            
            # Add IPO distributions if any
            if ipo_schedules:
                for schedule in ipo_schedules:
                    projected_values += sum(schedule['distribution_amounts'])
            
            # Add remaining companies at their current book value
            companies_with_exits = set(exit_timeline["Company"].tolist())
            for company_rem in company_net_investments.keys():
                if company_rem not in companies_with_exits:
                    net_investment_rem = company_net_investments.get(company_rem, 0)
                    if net_investment_rem > 0:
                        comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                        if not comp_data_rem.empty:
                            book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                            if not book_row.empty:
                                projected_values += book_row.iloc[0]["Book Value"]
            
            # Calculate total fees from fee_tracker
            total_fees = 0
            if fee_tracker:
                total_fees = sum(
                    fee_info.get("Management Fees", 0) + 
                    fee_info.get("Capital Call Fees", 0) + 
                    fee_info.get("Capital Call PR", 0) 
                    for fee_info in fee_tracker
                )
            
            # Net projected values after fees
            net_projected_values = projected_values - total_fees
            
            net_moic = net_projected_values / total_historical_cash_flows_net if total_historical_cash_flows_net > 0 else 0
            
        except Exception as e:
            net_moic = 0
            st.error(f"Error calculating Net MOIC: {str(e)}")
        
        # Add blank separator row
        all_cash_flows_for_display_net.append({
            'Fund Name': '',
            'Deal Name': '',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': ''
        })
        
        # Add overall Net IRR row at the bottom
        all_cash_flows_for_display_net.append({
            'Fund Name': fund_name,
            'Deal Name': 'Net IRR',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': f"{net_irr_pct:.2f}%"
        })
        
        # Add overall Net MOIC row at the bottom
        all_cash_flows_for_display_net.append({
            'Fund Name': fund_name,
            'Deal Name': 'Net MOIC',
            'Realized/Unrealized': '',
            'Date': '',
            'Cash Flows': f"{net_moic:.2f}x"
        })
        
        # Create DataFrame
        df_net = pd.DataFrame(all_cash_flows_for_display_net)
        
        # Sort by date (oldest to newest), but keep summary rows at the bottom
        summary_rows_net = df_net[df_net['Date'] == ''].copy()  # Blank and summary rows
        data_rows_net = df_net[df_net['Date'] != ''].copy()    # Actual cash flow rows
        
        # Sort data rows by date
        if not data_rows_net.empty:
            data_rows_net['Date_Sort'] = pd.to_datetime(data_rows_net['Date'], format='%m/%d/%Y', errors='coerce')
            data_rows_net = data_rows_net.sort_values('Date_Sort').drop('Date_Sort', axis=1)
        
        # Combine sorted data rows with summary rows
        df_net = pd.concat([data_rows_net, summary_rows_net], ignore_index=True)
        
        # Display the table
        st.dataframe(
            df_net,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Fund Name": st.column_config.TextColumn("Fund Name", width="medium"),
                "Deal Name": st.column_config.TextColumn("Deal Name", width="medium"),
                "Realized/Unrealized": st.column_config.TextColumn("Realized/Unrealized", width="medium"),
                "Date": st.column_config.TextColumn("Date", width="medium"),
                "Cash Flows": st.column_config.TextColumn("Cash Flows", width="medium")
            }
        )
        
        # Display summary info
        st.info(f"📊 **Dynamically Calculated Net IRR: {net_irr_pct:.2f}%** | **Net MOIC: {net_moic:.2f}x** (After management fees and costs)")
        
        # Add IPO scenario indicator
        if ipo_schedules:
            ipo_companies = [schedule['company_name'] for schedule in ipo_schedules]
            st.info(f"🔄 **IPO Scenario Active for:** {', '.join(ipo_companies)}")
        
    except Exception as e:
        st.error(f"Error displaying net IRR cash flows: {str(e)}")
        import traceback
        print(f"Net IRR cash flows error: {traceback.format_exc()}") 


def calculate_gross_irr(ipo_schedules, edited_df, company_net_investments, data_df=None):
    """
    Calculate Gross IRR value for use in main metrics display.
    Returns the Gross IRR as a decimal (e.g., 0.1502 for 15.02%).
    """
    try:
        import pandas as pd
        from datetime import datetime
        from financial_calculations import xirr, aggregate_cashflows_by_date
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            return None
        
        # Generate historical cash flows from actual data
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Build fund cash flows starting with historical data
        fund_flows = historical_flows.copy()
        
        # Process exits
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Process each exit date
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            exit_value_total = 0
            
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                gross_investment = get_gross_invested_capital(company_name)
                
                if gross_investment > 0:
                    exit_value = gross_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name in ipo_company_map:
                        # Skip the single exit - it will be replaced by IPO distributions
                        continue
                    else:
                        # Regular exit
                        exit_value_total += exit_value
            
            # Add total exit value to fund flows (excluding IPO companies)
            if exit_value_total > 0:
                fund_flows.append((exit_date_proc, exit_value_total))
        
        # Add IPO distributions if any
        if ipo_schedules:
            for schedule in ipo_schedules:
                for i, (dist_date, dist_amount) in enumerate(zip(
                    schedule['distribution_dates'], 
                    schedule['distribution_amounts']
                )):
                    # Add to fund flows
                    fund_flows.append((dist_date, dist_amount))
        
        # Add remaining companies at their current book value
        companies_with_exits = set(exit_timeline["Company"].tolist())
        
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                gross_investment_rem = get_gross_invested_capital(company_rem)
                if gross_investment_rem > 0:
                    # Get the latest date for this company
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        latest_date_rem = comp_data_rem["Date"].max()
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        
                        if not book_row.empty:
                            book_value = book_row.iloc[0]["Book Value"]
                            fund_flows.append((latest_date_rem, book_value))
        
        # Calculate Gross IRR using dynamic cash flows
        fund_flows_agg = aggregate_cashflows_by_date(fund_flows)
        overall_irr = xirr(fund_flows_agg)
        
        return overall_irr
        
    except Exception as e:
        print(f"Error calculating Gross IRR: {str(e)}")
        return None


def calculate_net_irr(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee):
    """
    Calculate Net IRR value for use in main metrics display.
    Returns the Net IRR as a decimal (e.g., 0.1002 for 10.02%).
    """
    try:
        import pandas as pd
        from datetime import datetime
        from financial_calculations import xirr, aggregate_cashflows_by_date, calculate_management_fees_with_hurdle
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            return None
        
        # Generate historical cash flows from actual data (same as gross)
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Build fund cash flows starting with historical data
        fund_flows_net = historical_flows.copy()
        
        # Process exits with fees
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Track whether we've processed first exit (for fee calculation)
        first_exit_processed = False
        remaining_fee_balance = fee_balance
        last_fee_date = fee_date
        
        # Process each exit date with fees
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            # Calculate fees for this exit period
            is_first_exit = not first_exit_processed
            
            # Calculate management fees for this period
            fee_details = calculate_management_fees_with_hurdle(
                last_fee_date, exit_date_proc, remaining_fee_balance, q_fee,
                hurdle_rate=0.08,
                include_capital_calls=True,
                is_first_exit=is_first_exit
            )
            
            total_period_fees = fee_details['total_fees']
            
            # Calculate total exit value for this period
            period_exit_value = 0
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                net_investment = company_net_investments.get(company_name, 0)
                
                if net_investment > 0:
                    exit_value = net_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name in ipo_company_map:
                        # Skip the single exit - it will be replaced by IPO distributions
                        continue
                    else:
                        # Regular exit
                        period_exit_value += exit_value
            
            # Add net exit value (after fees) to fund flows
            net_exit_value = period_exit_value - total_period_fees
            if net_exit_value > 0:
                fund_flows_net.append((exit_date_proc, net_exit_value))
            
            # Update for next period
            first_exit_processed = True
            remaining_fee_balance = 0  # Fees paid, balance reset
            last_fee_date = exit_date_proc
        
        # Add IPO distributions if any schedules exist
        if ipo_schedules:
            for schedule in ipo_schedules:
                for i, (dist_date, dist_amount) in enumerate(zip(
                    schedule['distribution_dates'], 
                    schedule['distribution_amounts']
                )):
                    # Calculate fees for this IPO distribution
                    dist_date = pd.to_datetime(dist_date)
                    
                    # For IPO distributions, calculate fees (simplified approach)
                    fee_ratio = 0.02  # Approximate management fee ratio
                    net_distribution = dist_amount * (1 - fee_ratio)
                    
                    # Add net distribution to fund flows
                    fund_flows_net.append((dist_date, net_distribution))
        
        # Add remaining companies at their current book value (same as gross)
        companies_with_exits = set(exit_timeline["Company"].tolist())
        
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                net_investment_rem = company_net_investments.get(company_rem, 0)
                if net_investment_rem > 0:
                    # Get the latest date for this company
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        latest_date_rem = comp_data_rem["Date"].max()
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        
                        if not book_row.empty:
                            book_value = book_row.iloc[0]["Book Value"]
                            fund_flows_net.append((latest_date_rem, book_value))
        
        # Calculate Net IRR using dynamic cash flows after fees
        fund_flows_net_agg = aggregate_cashflows_by_date(fund_flows_net)
        net_irr = xirr(fund_flows_net_agg)
        
        return net_irr
        
    except Exception as e:
        print(f"Error calculating Net IRR: {str(e)}")
        return None 


def calculate_gross_moic(ipo_schedules, edited_df, company_net_investments, data_df=None):
    """
    Calculate Gross MOIC value for use in main metrics display.
    Returns the Gross MOIC as a decimal (e.g., 1.71 for 1.71x).
    """
    try:
        import pandas as pd
        from datetime import datetime
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            return None
        
        # Generate historical cash flows from actual data
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Total historical cash flows (net invested capital: contributions minus early distributions)
        total_historical_cash_flows = abs(sum(amount for date, amount in historical_flows))
        
        # Process exits
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Projected values (exit values, IPO distributions, unrealized book values)
        projected_values = 0
        
        # Add exit values from non-IPO companies
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                gross_investment = get_gross_invested_capital(company_name)
                
                if gross_investment > 0:
                    exit_value = gross_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name not in ipo_company_map:
                        projected_values += exit_value
        
        # Add IPO distributions if any
        if ipo_schedules:
            for schedule in ipo_schedules:
                projected_values += sum(schedule['distribution_amounts'])
        
        # Add remaining companies at their current book value
        companies_with_exits = set(exit_timeline["Company"].tolist())
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                gross_investment_rem = get_gross_invested_capital(company_rem)
                if gross_investment_rem > 0:
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        if not book_row.empty:
                            projected_values += book_row.iloc[0]["Book Value"]
        
        gross_moic = projected_values / total_historical_cash_flows if total_historical_cash_flows > 0 else 0
        return gross_moic
        
    except Exception as e:
        print(f"Error calculating Gross MOIC: {str(e)}")
        return None


def calculate_net_moic(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee):
    """
    Calculate Net MOIC value for use in main metrics display.
    Returns the Net MOIC as a decimal (e.g., 1.65 for 1.65x).
    """
    try:
        import pandas as pd
        from datetime import datetime
        
        # Check if we have the required data
        if edited_df is None or not company_net_investments or data_df is None:
            return None
        
        # Generate historical cash flows from actual data
        historical_flows = []
        for _, row_data_hist in data_df.iterrows():
            if not pd.isna(row_data_hist["Date"]):
                historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
        
        # Total historical cash flows (net invested capital: contributions minus early distributions)
        total_historical_cash_flows_net = abs(sum(amount for date, amount in historical_flows))
        
        # Process exits
        exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
        exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
        
        # Group exits by date
        exit_dates = {}
        for _, row_data_exit in exit_timeline.iterrows():
            exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
            if pd.notna(exit_date_val):
                if exit_date_val not in exit_dates:
                    exit_dates[exit_date_val] = []
                exit_dates[exit_date_val].append(row_data_exit)
        
        # Create IPO company mapping for quick lookup
        ipo_company_map = {}
        if ipo_schedules:
            for schedule in ipo_schedules:
                ipo_company_map[schedule['company_name']] = schedule
        
        # Calculate projected values and fees using actual fee tracker data
        projected_values = 0
        total_fees = 0
        
        # Add exit values from non-IPO companies
        for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
            for exit_info in companies_exiting:
                company_name = exit_info["Company"]
                moic = exit_info["MOIC"]
                net_investment = company_net_investments.get(company_name, 0)
                
                if net_investment > 0:
                    exit_value = net_investment * moic
                    
                    # Check if this company is in IPO scenario
                    if company_name not in ipo_company_map:
                        projected_values += exit_value
        
        # Add IPO distributions if any
        if ipo_schedules:
            for schedule in ipo_schedules:
                projected_values += sum(schedule['distribution_amounts'])
        
        # Add remaining companies at their current book value
        companies_with_exits = set(exit_timeline["Company"].tolist())
        for company_rem in company_net_investments.keys():
            if company_rem not in companies_with_exits:
                net_investment_rem = company_net_investments.get(company_rem, 0)
                if net_investment_rem > 0:
                    comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                    if not comp_data_rem.empty:
                        book_row = comp_data_rem[comp_data_rem["Book Value"] > 0].sort_values("Date", ascending=False)
                        if not book_row.empty:
                            projected_values += book_row.iloc[0]["Book Value"]
        
        # Calculate total fees from fee_tracker
        if fee_tracker:
            total_fees = sum(
                fee_info.get("Management Fees", 0) + 
                fee_info.get("Capital Call Fees", 0) + 
                fee_info.get("Capital Call PR", 0) 
                for fee_info in fee_tracker
            )
        
        # Net projected values after fees
        net_projected_values = projected_values - total_fees
        
        net_moic = net_projected_values / total_historical_cash_flows_net if total_historical_cash_flows_net > 0 else 0
        return net_moic
        
    except Exception as e:
        print(f"Error calculating Net MOIC: {str(e)}")
        return None 





